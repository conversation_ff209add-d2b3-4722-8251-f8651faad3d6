import request from '@/config/axios'
import { TeamVO } from '@/api/project/team'

// 项目基础信息 VO
export interface BasicsVO {
  id?: number // 项目ID
  number?: string // 项目编号
  name?: string // 项目名称
  sort?: number // 显示顺序
  platform?: string // 项目平台
  type?: string // 项目类型
  mold?: string // 开发类型
  level?: string // 项目等级
  points?: number // 项目积分
  managers?: number[] // 项目经理ids
  targetMarket?: string // 针对市场
  targetCustomer?: string // 针对客户
  releaseDate?: Date // 产品预计发布时间
  planOrderDate?: Date // 产品预计下单时间
  actualOrderDate?: Date //产品实际下单时间
  status?: number // 项目状态（0进行中，1暂停，2完结,3冻结）
  progress?: number // 项目进度
  categoryIds?: number[]
  remark?: string // 备注
  teams?: TeamVO[] // 项目团队
  approve?: any // 审批流程
  permission?: any[] // 权限信息
  log?: any[] //修改日志
  erpCode?: string // erp项目编码
  planExpense?: number // 预计费用
  processInstanceId?: string //
  createTime?: string
}

// 项目基础信息 API
export const BasicsApi = {
  /** 创建项目 */
  createBasics: (data: BasicsVO) => {
    return request.post({ url: '/project/basics/create', data })
  },
  /** 更新项目 */
  updateBasics: (data: BasicsVO) => {
    return request.post({ url: '/project/basics/update', data })
  },
  /** 查询项目 */
  getBasics: (id: number) => {
    return request.get({ url: `/project/basics/get/${id}` })
  },
  getSimpleBasics: (id: number) => {
    return request.get({ url: `/project/basics/get-simple/${id}` })
  },
  /** 查询项目列表 */
  getBasicsList: (data: any) => {
    return request.post({ url: '/project/basics/list', data })
  },
  getQueryCategoryList: (data: any) => {
    return request.post({ url: '/project/basics/list-category', data })
  },
  /** 查询项目日志 */
  getBasicsLogPage: (params: any) => {
    return request.get({ url: '/project/basics/page-log', params })
  },
  /** 重启或暂停项目 */
  pauseRestartBasics: (id: number) => {
    return request.get({ url: `/project/basics/pause-restart/${id}` })
  },
  /** 查询项目权限信息 */
  getPermission: (id: number) => {
    return request.get({ url: `/project/basics/get-permission/${id}` })
  },
  /** 获取所有项目的基础信息 */
  getSimpleList: () => {
    return request.get({ url: '/project/basics/get-simple-list' })
  },
  /** 更新项目的ERP项目编码 */
  updateErpCode: (params: any) => {
    return request.get({ url: '/project/basics/update-erp-code', params })
  },
  /** 更新项目预计费用 */
  updatePlanExpense: (params: any) => {
    return request.get({ url: '/project/basics/update-plan-expense', params })
  },
  updateActualOrderDate: (params: any) => {
    return request.get({ url: '/project/basics/update-actual-date', params })
  },
  /** 导出项目信息Excel */
  export: (data: any) => {
    return request.downloadPost({ url: '/project/basics/export-excel', data })
  },
  /** 获取项目变更申请信息列表 */
  getBasicsChangeApplicationList: () => {
    return request.get({ url: '/project/basics/get-change-application-list' })
  }
}
