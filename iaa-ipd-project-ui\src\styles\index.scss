@import './var.css';
@import './FormCreate/index.scss';
@import './theme.scss';
@import 'element-plus/theme-chalk/dark/css-vars.css';

* {
  box-sizing: border-box;
}

html {
  font-size: 15px !important;
}
@media (max-width: 1680px) {
  html {
    font-size: 13px !important; /* 小屏设备字体减小 */
  }
}

@media (min-width: 2100px) {
  html {
    font-size: 20px !important; /* 大屏设备字体增大 */
  }
}

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

.no-radius .el-progress-bar__outer {
  border-radius: 0 !important;

  & .el-progress-bar__inner {
    border-radius: 0 !important;
  }
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.form-no-bottom-margin .el-form-item {
  margin-bottom: 0px !important;
}

.custom-form {
  .el-form-item {
    margin-bottom: 5px !important;

    .el-form-item__content {
      &:has(> .el-form-item__error) {
        margin-bottom: 14px;
      }
    }

    .is-disabled {
      .el-input__wrapper,
      .el-input__inner,
      .el-select__wrapper,
      .el-textarea__inner {
        box-shadow: none !important;
        --el-input-placeholder-color:var(--el-disabled-bg-color) !important;
        &::placeholder {
          -webkit-text-fill-color: var(--el-disabled-bg-color) !important;
        }
        &::-webkit-input-placeholder {
          -webkit-text-fill-color: var(--el-disabled-bg-color) !important;
        }
      }
      .el-select__placeholder{
        display: none;
      }
    }

    .is-disabled.el-select__wrapper {
      box-shadow: none !important;
      &::placeholder {
        -webkit-text-fill-color: var(--el-disabled-bg-color) !important;
      }
    }
  }
}

.custom-border-form {
  border-top: 1px solid #e9e9e9;
  border-right: 1px solid #e9e9e9;

  .el-form-item {
    margin-bottom: 0px !important;

    .el-form-item__content,
    .el-form-item__label {
      height: auto;
      color: #969696;
      background-color: #f8f8f8;
      border-bottom: 1px solid #e9e9e9;
      border-left: 1px solid #e9e9e9;
    }

    .el-form-item__content {
      background-color: #fff !important;

      &:has(> .el-form-item__error) {
        margin-bottom: 14px;
      }
    }

    .el-input.is-disabled .el-input__wrapper {
      background-color: #fff;
    }

    .el-input__wrapper {
      box-shadow: none !important;
    }
    .el-textarea__inner {
      box-shadow: none !important;
    }
    .el-select__wrapper {
      box-shadow: none !important;
    }

    .el-textarea.is-disabled .el-textarea__inner {
      background-color: #fff;
      -webkit-text-fill-color: var(--primary-text-color);
    }

    .el-input.is-disabled .el-input__inner {
      -webkit-text-fill-color: var(--primary-text-color);
    }

    .el-select__wrapper.is-disabled {
      background-color: #fff;
      -webkit-text-fill-color: var(--primary-text-color);
    }
  }
}

// .custom-form .el-form-item .el-input__wrapper,
// .el-select__wrapper {
//   box-shadow: none;
// }

.audit-drawer {
  border: 5px dashed var(--el-color-warning);
}

.el-drawer {
  pointer-events: auto;
}

.el-drawer__header {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  margin-bottom: 0 !important;
  // border-bottom: 0.3px dashed var(--el-color-warning);
  background: linear-gradient(to right, #94c9ff, var(--el-color-primary-light-3));
  .el-icon {
    fill: #fff !important;
    color: #fff !important;
  }
}

.el-drawer__title {
  color: #fff;
  font-weight: 600;
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.15),
    0 0 3px rgba(255, 255, 255, 0.2);
}

.el-drawer__body {
  padding-top: 0px !important;

  .el-tabs__item {
    font-size: 1rem;
    // font-weight: bold;
  }
}

.el-drawer__footer {
  border-top: 0.3px dashed var(--el-color-warning);
  padding-bottom: 10px !important;
  text-align: center !important;
}

.drawer-tabs {
  position: sticky;
  padding-top: -20px;
  top: 0;
  z-index: 99;
  background-color: #fff;
}

.el-dialog {
  padding: 0 !important;
  border-radius: 10px;

  .el-dialog__header {
    border-radius: 10px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    height: 40px !important;
    background: linear-gradient(
      to right,
      var(--el-color-primary-light-3),
      var(--el-color-primary-light-5)
    );
    color: #fff;
    font-weight: bold;
    text-shadow:
      0 1px 2px rgba(0, 0, 0, 0.15),
      0 0 3px rgba(255, 255, 255, 0.2);
    div {
      height: 40px !important;
      color: #fff;
    }

    svg {
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 0 !important;
  }

  .file-viewer-container {
    height: 80vh;
  }

  .el-dialog__footer {
    padding: 5px;
  }
}

.file-viewer-container {
  min-height: 70vh;
  iframe {
    min-height: 70vh;
  }
}

.maximized .file-viewer-container {
  height: calc(100vh - 75px);
}

.el-dialog.is-fullscreen .file-viewer-container {
  height: calc(100vh - 75px);
}

.is-disabled {
  .el-select__selected-item,
  .el-textarea__inner,
  .el-input__inner {
    color: var(--primary-text-color) !important;
    -webkit-text-fill-color: var(--primary-text-color) !important;
    font-size: 1rem;
  }

  .el-input-number__decrease,
  .el-input-number__increase,
  .el-select__suffix {
    display: none;
  }

  .el-input__wrapper {
    padding: 1px !important;
  }
}

.el-input__inner,
.el-textarea__inner,
.el-form-item__label {
  font-size: 0.9rem;
}

.el-progress-bar__outer {
  border-radius: 0;
}

.w-e-bar-divider {
  height: 2vw;
}
.w-e-bar-item {
  height: 2vw;

  & > * > svg {
    width: 1vw;
    height: 1vw;
  }

  & > button {
    height: 2vw;
    font-size: 1rem;
    padding: 0 4px;
  }

  & > .w-e-bar-item-menus-container {
    margin-top: 2vw;
  }
}

.no-modal-mask-layer {
  pointer-events: none;
}

.custom-collapse {
  .el-collapse-item__header {
    border: none !important;
    border-left: 4px solid var(--el-color-primary) !important;
    border-bottom: 0.3px dashed var(--el-color-primary) !important;
    padding-left: 4px;
    height: 30px;
    background-color: var(--el-color-primary-light-9);
    border-radius: 3px;
    font-size: 14px;
  }
  .el-collapse-item {
    border: none !important;
  }
  .el-collapse-item__content {
    padding: 10px !important;
    background-color: #fafafa70;
  }
}

.audit-dialog {
  height: 80vh;
  .el-dialog__body {
    overflow: auto;
    height: calc(100% - 60px);
  }
}

.is-fullscreen.audit-dialog {
  height: 100vh;
}

.vxe-table-export--panel-table {
  tr:nth-child(2),
  tr:nth-child(3),
  tr:nth-child(5) {
    display: none;
  }
}

.over-tip {
  white-space: nowrap; /* 禁止文字换行 */
  width: calc(100% - 160px);
  overflow: hidden;
  margin-left: 10px;
  background-color: #f1f1f1;
  border-radius: 4px;
  span {
    display: inline-block;

    font-size: 0.9rem;
    height: 2rem;
    line-height: 2rem;
  }

  span:hover {
    animation: scroll-text 30s linear infinite;
  }
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.vxe-tooltip--wrapper {
  z-index: 3000 !important;
}

.el-dialog.is-fullscreen .el-dialog__body {
  min-height: calc(100vh - 40px - 50px);
}