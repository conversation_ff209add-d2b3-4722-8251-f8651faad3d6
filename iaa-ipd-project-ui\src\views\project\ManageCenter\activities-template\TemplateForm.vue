<template>
  <NoModalDrawer v-model="show" title="模板信息" size="50%" :before-close="handleClose">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">模板信息</div>
        <div>
          <el-tooltip content="删除活动模板">
            <el-button link @click="onDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <template #default>
      <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
        <el-form
          size="small"
          ref="formRef"
          label-width="95px"
          :rules="formRules"
          :model="formData"
          :disabled="formData.id && updateStatus === 0"
        >
          <el-collapse-item title="基础信息" name="1">
            <el-form-item label="父活动" prop="parentId">
              <el-tree-select
                filterable
                v-model="formData.parentId"
                :data="[{ id: 0, name: '根节点', children: handleTree(props.templateList) }]"
                :props="treeTemplateListOptions"
                node-key="id"
                check-strictly
              />
              <!-- <el-select v-model="formData.parentId" filterable>
                <el-option label="根节点" :value="0" />
                <el-option
                  v-for="template in props.templateList"
                  :key="template.id"
                  :label="`${template.orderNo} ${template.name}`"
                  :value="template.id"
                />
              </el-select> -->
            </el-form-item>
            <el-form-item label="活动标题" prop="name">
              <el-input v-model="formData.name" show-word-limit maxlength="255" />
            </el-form-item>
            <el-form-item label="活动别名">
              <el-input v-model="formData.alias" show-word-limit maxlength="255" />
            </el-form-item>
            <el-form-item label="活动内容" prop="content">
              <el-input
                v-model="formData.content"
                type="textarea"
                :rows="3"
                show-word-limit
                maxlength="255"
              />
            </el-form-item>
            <el-form-item label="活动描述">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="6"
                show-word-limit
                maxlength="1000"
              />
            </el-form-item>
            <el-form-item label="序号" prop="sort">
              <el-input-number v-model="formData.sort" :min="1" :step="1" />
            </el-form-item>
            <el-form-item label="默认角色">
              <el-select v-model="formData.defaultRole" clearable filterable>
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关键活动" prop="isCrux">
              <el-switch
                v-model="formData.isCrux"
                inline-prompt
                active-text="是"
                inactive-text="否"
                :active-value="true"
                :inactive-value="false"
              />
            </el-form-item>
            <el-form-item label="默认工时(天)" prop="defaultManDay">
              <el-input-number v-model="formData.defaultManDay" :min="0" :step="1" />
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item title="规则信息" name="3">
            <vxe-table
              :header-cell-style="{ padding: 0, fontSize: '1rem' }"
              border
              align="center"
              round
              :data="[activitiesRules]"
              :loading="tableLoading"
              :header-cell-config="{height:30}"
              :cell-config="{height:30}"
            >
              <vxe-colgroup
                v-for="platform in getStrDictOptions('project_platform')"
                :key="platform.value"
                :title="platform.label"
                :field="`platform_${platform.value}`"
              >
                <vxe-colgroup
                  v-for="type in getStrDictOptions('project_type')"
                  :key="type.value"
                  :title="type.label"
                  :field="`platform_${platform.value}_type_${type.value}`"
                >
                  <template v-for="level in getStrDictOptions('project_level')" :key="level.value">
                    <vxe-column
                      :title="level.label"
                      :field="`${platform.value}_${type.value}_${level.value}`"
                      v-if="
                        (platform.value === '10' && ['10', '11', '12'].includes(level.value)) ||
                        (platform.value === '20' &&
                          type.value === '10' &&
                          ['12', '13', '14', '15', '16'].includes(level.value)) ||
                        (platform.value === '20' &&
                          type.value === '20' &&
                          ['13', '14', '15', '16', '17'].includes(level.value))
                      "
                      width="60px"
                    >
                      <template #default="{ row }">
                        <el-button
                          circle
                          :type="
                            row[platform.value][type.value][level.value] === 1
                              ? 'info'
                              : row[platform.value]?.[type.value][level.value] === 2
                                ? 'success'
                                : undefined
                          "
                          @click="changeRule(row, platform.value, type.value, level.value)"
                          :disabled="formData.id && updateStatus === 0"
                        />
                      </template>
                    </vxe-column>
                  </template>
                </vxe-colgroup>
              </vxe-colgroup>
            </vxe-table>
          </el-collapse-item>
          <el-collapse-item title="工时信息" name="6">
            <vxe-table
              border
              :header-cell-style="{ padding: 0, fontSize: '1rem' }"
              align="center"
              round
              :data="[activitiesWorkingHours]"
              :header-cell-config="{height:30}"
              :cell-config="{height:30}"
            >
              <vxe-column
                v-for="dict in getStrDictOptions('project_level')"
                :key="dict.value"
                :title="dict.label"
                :field="dict.value"
              >
                <template #default="{ row }">
                  <el-input-number
                    v-model="row[dict.value]"
                    :min="0"
                    :step="1"
                    :precision="2"
                    class="!w-full"
                  />
                </template>
              </vxe-column>
            </vxe-table>
          </el-collapse-item>
          <el-collapse-item title="输出信息" name="2">
            <el-form-item label="输出类型" prop="targetType">
              <el-select v-model="formData.targetType" @change="handleTargetTypeChange">
                <el-option
                  v-for="dict in getIntDictOptions('project_activities_target_type')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="附件模板列表"
              v-if="formData.targetType === 0"
              prop="targetTemplateIds"
            >
              <div class="w-full">
                <div>
                  <el-button @click="fileTemplateShow = true">选择文件模板</el-button>
                </div>
                <div
                  v-for="file in fileTemplateList.filter((el: any) =>
                    formData?.targetTemplateIds?.includes(el.id)
                  )"
                  :key="file.id"
                  class="flex justify-between w-full pb-5px"
                >
                  <el-link
                    type="primary"
                    @click="officeEditorRef.open(file.infraFileId, file.name)"
                  >
                    {{ file.name }}
                  </el-link>
                  <el-button type="danger" plain @click="unCheckedFileTemplate(file.id)">
                    X
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              label="PLM对接方式"
              v-else-if="formData.targetType === 1"
              prop="targetDockingId"
            >
              <el-select v-model="formData.targetDockingId">
                <el-option
                  v-for="dict in getIntDictOptions('project_activities_target_docking_plm')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="ERP对接方式"
              v-else-if="formData.targetType === 2"
              prop="targetDockingId"
            >
              <el-select v-model="formData.targetDockingId">
                <el-option
                  v-for="dict in getIntDictOptions('project_activities_target_docking_erp')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item title="输出审签信息" name="5">
            <el-form-item label="审签流程" prop="targetApproveId">
              <el-select v-model="formData.targetApproveId" filterable @change="approveChange">
                <el-option label="无" value="-1" />
                <el-option
                  v-for="item in props.definitionList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <vxe-table
              v-if="formData.targetApproveId && formData.targetApproveId != '-1'"
              :data="approveNode"
              :header-cell-style="{ padding: '5px' }"
              :cell-style="{ padding: '5px' }"
              border
              stripe
            >
              <vxe-column title="流程节点" field="taskName" />
              <vxe-column title="是否需要设置审批人" field="hasSetting">
                <template #default="{ row }">
                  <el-tag type="success" v-if="row.hasSetting">是</el-tag>
                </template>
              </vxe-column>
              <vxe-column title="审批人类型" field="userType">
                <template #default="{ row }">
                  <el-select v-model="row.userType" @change="row.userIds = undefined">
                    <el-option label="项目角色" :value="0" />
                    <el-option label="流程用户分组" :value="1" />
                    <el-option label="后台逻辑判定" :value="2" />
                    <!-- <el-option label="指定人员" :value="2" /> -->
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column title="审批人" field="userIds">
                <template #default="{ row }">
                  <el-select
                    v-model="row.userIds"
                    v-if="row.userType === 0"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                  <el-select
                    v-model="row.userIds"
                    v-else-if="row.userType === 1"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="item in props.userGroupList"
                      :key="item.id"
                      :label="item.name"
                      :value="String(item.id)"
                    />
                  </el-select>
                </template>
              </vxe-column>
            </vxe-table>
          </el-collapse-item>
          <el-collapse-item title="依赖信息" name="4">
            <vxe-table
              :data="activitiesDependency"
              :header-cell-style="{ padding: '5px' }"
              :cell-style="{ padding: '5px' }"
              border
              stripe
            >
              <vxe-column title="活动" field="preTemplateId">
                <template #default="{ row }">
                  <el-select filterable v-model="row.preTemplateId">
                    <el-option
                      v-for="activities in templateList?.filter(
                        (el: any) => el.targetType !== 3 && formData.id && el.id !== formData.id
                      )"
                      :key="activities.id"
                      :label="activities.name"
                      :value="activities.id"
                    />
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column title="关系" field="dependencyType">
                <template #default="{ row }">
                  <el-select v-model="row.dependencyType">
                    <el-option
                      v-for="dict in getStrDictOptions('project_activities_relation')"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column title="操作" field="action" width="70px" align="center">
                <template #default="{ rowIndex }">
                  <el-button type="danger" @click="deleteDependency(rowIndex)" plain>
                    删除
                  </el-button>
                </template>
              </vxe-column>
            </vxe-table>
            <el-button class="w-full" @click="addDependency" type="primary" plain>
              添加依赖
            </el-button>
          </el-collapse-item>
        </el-form>
      </el-collapse>
    </template>
    <template #footer>
      <div class="drawer-footer" v-if="formType === 'create'">
        <el-button type="primary" @click="createActivitiesTemplate()">保存</el-button>
        <el-button type="primary" @click="createActivitiesTemplate(true)">保存并继续添加</el-button>
      </div>
      <div class="drawer-footer" v-else-if="formType === 'update'">
        <el-button type="warning" v-if="updateStatus === 0" @click="updateStatus = 1">
          修改
        </el-button>
        <el-button type="warning" v-if="updateStatus === 1" @click="updateActivitiesTemplate">
          保存修改
        </el-button>
      </div>
    </template>
  </NoModalDrawer>
  <Dialog title="文件模板选择" v-model="fileTemplateShow">
    <div class="head-container">
      <el-input v-model="templateName" class="mb-20px" clearable placeholder="请输入部门名称">
        <template #prefix>
          <Icon icon="ep:search" />
        </template>
      </el-input>
    </div>
    <div class="overflow-auto h-70vh">
      <el-tree
        ref="fileTemplateTreeRef"
        :data="fileTemplateList"
        :props="{ label: 'name' }"
        node-key="id"
        show-checkbox
        :filter-node-method="filterNode"
      />
    </div>
    <template #footer>
      <el-button type="primary" @click="handleSubmitFileTemplate">确定</el-button>
    </template>
  </Dialog>

  <OfficeEditor ref="officeEditorRef" />
</template>

<script lang="ts" setup>
import { FormRules } from 'element-plus'
import { getStrDictOptions, DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import * as DefinitionApi from '@/api/bpm/definition'
import { cloneDeep, debounce } from 'lodash-es' // 引入 lodash 的 debounce 函数
import { propTypes } from '@/utils/propTypes'
import { handleTree } from '@/utils/tree'

const message = useMessage()
const show = ref(false)
const fileTemplateShow = ref(false)
const fileTemplateTreeRef = ref()
const officeEditorRef = ref()
const formRef = ref()
const activeNames = ref<string[]>(['1', '2', '3', '4', '5', '6'])
const templateName = ref('')

const tableLoading = ref(false)

const formType = ref('create')
const updateStatus = ref(0)

const props = defineProps({
  templateList: propTypes.arrayOf<any>([]).def([]),
  definitionList: propTypes.arrayOf<any>([]).def([]),
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  userGroupList: propTypes.arrayOf<any>([]).def([])
})

const treeTemplateListOptions = {
  label: 'name'
}

const formData = ref({
  id: undefined,
  parentId: undefined,
  sort: undefined,
  categoryId: undefined,
  name: undefined,
  alias: undefined,
  content: undefined,
  description: undefined,
  defaultRole: undefined,
  targetType: undefined,
  targetDockingId: undefined,
  targetTemplateIds: undefined as unknown as number[],
  targetApproveId: undefined,
  isCrux: undefined,
  defaultManDay: undefined
})

const formRules = reactive<FormRules>({
  parentId: [{ required: true, message: '请选择父活动', trigger: 'blur' }],
  name: [{ required: true, message: '请输入活动内容', trigger: 'blur' }],
  targetType: [{ required: true, message: '请选择输出类型', trigger: 'blur' }],
  defaultManDay: [{ required: true, message: '请输入默认工时', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入序号', trigger: 'blur' }],
  targetDockingId: [{ required: true, message: '请选择对接方式', trigger: 'blur' }],
  targetTemplateIds: [{ required: true, message: '请选择模板', trigger: 'blur' }],
  targetApproveId: [{ required: true, message: '请选择输出物审批流程', trigger: 'blur' }]
})

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 监听deptName */
watch(templateName, (val) => {
  fileTemplateTreeRef.value!.filter(val)
})
const onDelete = async () => {
  await message.confirm('确认删除该节点？')
  await ActivitiesTemplateApi.deleteActivitiesTemplate(formData.value.id!)
  message.success('删除成功')
  emits('success')
  show.value = false
}

const approveChange = async () => {
  const userTask = await DefinitionApi.getProcessDefinition(
    undefined,
    formData.value.targetApproveId
  )
  approveNode.value = userTask?.startUserSelectTasks?.map((el) => {
    return {
      taskId: el.id,
      taskName: el.name,
      hasSetting: true,
      userType: undefined,
      userIds: undefined
    }
  })
}
// 审批节点信息
const approveNode = ref<any[]>([])

// 规则
const activitiesRules = ref<any>({})
// 初始化规则字典
// 该函数用于初始化不同平台、类型和级别的活动规则
const initRules = async () => {
  const platforms = await getStrDictOptions('project_platform')
  const types = await getStrDictOptions('project_type')
  const levels = await getStrDictOptions('project_level')

  platforms.forEach((platform) => {
    activitiesRules.value[platform.value] = {}
    types.forEach((type) => {
      activitiesRules.value[platform.value][type.value] = {}
      levels.forEach((level) => {
        if (
          (platform.value === '10' && ['10', '11', '12'].includes(level.value)) ||
          (platform.value === '20' &&
            type.value === '10' &&
            ['12', '13', '14', '15', '16'].includes(level.value)) ||
          (platform.value === '20' &&
            type.value === '20' &&
            ['13', '14', '15', '16', '17'].includes(level.value))
        ) {
          activitiesRules.value[platform.value][type.value][level.value] = 0
        }
      })
    })
  })
}
// 规则改变
const changeRule = (row: any, platform: string, type: string, level: string) => {
  row[platform][type][level]++
  if (row[platform][type][level] > 2) {
    row[platform][type][level] = 0
  }
}

const activitiesWorkingHours = ref<any>({})
const initWorkingHours = async () => {
  const levels = await getStrDictOptions('project_level')
  levels.forEach((level) => {
    activitiesWorkingHours.value[level.value] = 0
  })
}

// 依赖关系
const activitiesDependency = ref<any[]>([])
// 添加依赖信息
const addDependency = async () => {
  activitiesDependency.value.push({
    id: undefined,
    templateId: formData.value.id,
    preTemplateId: undefined,
    dependencyType: 'fs'
  })
}

// 删除依赖信息
const deleteDependency = async (index: number) => {
  await message.confirm('确定删除当前依赖行信息嘛?')
  activitiesDependency.value.splice(index, 1)
}

// 防抖处理函数
const handleTargetTypeChange = debounce(() => {
  tableLoading.value = true
  formData.value.targetDockingId = undefined
  formData.value.targetTemplateIds = undefined as unknown as number[]
  tableLoading.value = false
}, 300)

/** 选择文件模板 */
const handleSubmitFileTemplate = () => {
  const checkedFileTemplateIds = fileTemplateTreeRef.value.getCheckedKeys()
  if (!checkedFileTemplateIds || checkedFileTemplateIds.length === 0) {
    message.alertError('未选择文件模板')
    return
  }
  formData.value.targetTemplateIds = checkedFileTemplateIds
  fileTemplateShow.value = false
}
/** 取消选择文件模板 */
const unCheckedFileTemplate = (id: number) => {
  formData.value.targetTemplateIds = formData.value.targetTemplateIds.filter(
    (item: number) => item !== id
  )
}

const emits = defineEmits(['success'])
/** 创建活动模板 */
const createActivitiesTemplate = async (isContinue?: boolean) => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  delete activitiesRules.value._X_ROW_KEY
  delete activitiesWorkingHours.value._X_ROW_KEY
  await ActivitiesTemplateApi.createActivitiesTemplate({
    ...formData.value,
    rules: activitiesRules.value,
    dependencies: activitiesDependency.value,
    approveNodes: approveNode.value,
    workingHours: activitiesWorkingHours.value
  })
  message.success('创建成功')
  if (isContinue) {
    handleClose()
  } else {
    show.value = false
  }
  emits('success')
}
/** 更新活动模板 */
const updateActivitiesTemplate = async () => {
  await message.confirm('确定修改嘛？')
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  delete activitiesRules.value._X_ROW_KEY
  delete activitiesWorkingHours.value._X_ROW_KEY
  await ActivitiesTemplateApi.updateActivitiesTemplate({
    ...formData.value,
    rules: activitiesRules.value,
    dependencies: activitiesDependency.value,
    approveNodes: approveNode.value,
    workingHours: activitiesWorkingHours.value
  })
  message.success('修改成功')
  updateStatus.value = 0
  emits('success')
}

const open = (categoryId: string) => {
  formData.value.categoryId = categoryId as any
  initRules()
  initWorkingHours()
  show.value = true
  formType.value = 'create'
  handleClose()
}

const openRow = (row: any) => {
  formData.value = cloneDeep(row)
  activitiesRules.value = cloneDeep(row.rules)
  activitiesWorkingHours.value = cloneDeep(row.workingHours)
  if (!row.workingHours) {
    activitiesWorkingHours.value = {}
    initWorkingHours()
  }

  activitiesDependency.value = row.dependencies || []
  approveNode.value = row.approveNodes || []
  show.value = true
  formType.value = 'update'
  updateStatus.value = 0
}

const handleClose = (done?: () => void) => {
  if (done) {
    done()
  }
  formData.value = {
    id: undefined,
    parentId: undefined,
    sort: undefined,
    categoryId: formData.value.categoryId,
    name: undefined,
    alias: undefined,
    content: undefined,
    description: undefined,
    defaultRole: undefined,
    targetType: undefined,
    targetDockingId: undefined,
    targetTemplateIds: undefined as unknown as number[],
    targetApproveId: undefined,
    isCrux: undefined,
    defaultManDay: undefined
  }
  activitiesDependency.value = []
  approveNode.value = []
  initRules()
  initWorkingHours()
}

defineExpose({
  open,
  openRow
})
</script>
