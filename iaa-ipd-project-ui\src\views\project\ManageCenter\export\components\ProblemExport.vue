<template>
  <vxe-split-pane width="290px" min-width="290px" name="problem-condition">
    <div class="p-10px">
      <el-form id="problem-condition" class="custom-form" label-width="70px">
        <el-form-item label="项目">
          <el-input v-model="formData.basicsName" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.basicsStatus" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题描述">
          <el-input v-model="formData.content" type="textarea" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题类型">
          <el-select v-model="formData.type">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_type')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题等级">
          <el-select v-model="formData.level" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_problem_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题分类">
          <el-select v-model="formData.category" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_problem_category')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问题模块">
          <el-select v-model="formData.module" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_problem_module')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提出部门">
          <el-select v-model="formData.proposingDepartment" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_problem_proposing_department')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提出时间">
          <el-date-picker
            type="daterange"
            v-model="formData.timeOfProposal"
            unlink-panels
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="计划完成">
          <el-date-picker
            type="daterange"
            v-model="formData.timeOfPlan"
            unlink-panels
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="负责人">
          <UserAvatarList
            v-model="formData.direcotr!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
        <el-form-item label="执行人">
          <UserAvatarList
            v-model="formData.coordinate!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar custom size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="handleList"> 查询 </el-button>
          <el-button type="warning" size="small" @click="refresh"> 重置 </el-button>
        </template>
        <template #tools>
          <el-button type="primary" size="small" @click="handleExport"> 导出 </el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="86%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px' }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="problemList"
        :loading="loading"
        :export-config="{
          remote: true,
          exportMethod: handleExport
        }"
      >
        <vxe-column title="项目名称" field="basicsName" width="200" align="left" />
        <vxe-column title="项目等级" field="basicsLevel" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.basicsLevel) }}
          </template>
        </vxe-column>
        <vxe-column title="项目状态" field="basicsStatus" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.basicsStatus) }}
          </template>
        </vxe-column>
        <vxe-column
          title="描述"
          field="content"
          width="200"
          align="left"
        />
        <vxe-column title="图片" field="imgIds" width="60" align="center">
          <template #default="{ row }">
            <template v-if="row.imgIds?.length > 0">
              <img :src="row.imgIds?.[0]" style="width: 1.5vw; height: 1.5vw" />
              <div
                style="
                  position: absolute;
                  top: 0;
                  width: 1vw;
                  height: 1vw;
                  background-color: var(--el-color-primary-light-3);
                  border-radius: 1rem;
                  right: 0;
                  color: #fff;
                "
                >{{ row.imgIds?.length }}</div
              >
            </template>
          </template>
        </vxe-column>
        <vxe-column title="阶段" field="stage" width="90" align="center">
          <template #default="{ row }">
            {{ row.stageName }}
          </template>
        </vxe-column>
        <vxe-column
          title="状态"
          field="status"
          width="95"
          align="center"
        >
          <template #default="{ row }">
            <DictTag :type="'project_activities_status'" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="进度" field="progress" align="center" width="7%">
          <template #default="{ row }">
            {{ row.progress }}
          </template>
        </vxe-column>
        <vxe-column
          title="等级"
          field="level"
          width="90"
          align="center"
        >
          <template #default="{ row }">
            <DictTag :type="'project_problem_level'" :value="row.level" />
          </template>
        </vxe-column>
        <vxe-column
          title="分类"
          field="category"
          width="110"
          align="center"
        >
          <template #default="{ row }">
            <DictTag :type="'project_problem_category'" :value="row.category" />
          </template>
        </vxe-column>
        <vxe-column
          title="责任模块"
          field="module"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <DictTag :type="'project_problem_module'" :value="row.module" />
          </template>
        </vxe-column>
        <vxe-column
          title="原因分析"
          field="reason"
          width="200"
          align="left"
        />
        <vxe-column title="提出部门" field="proposingDepartment" width="100" align="center">
          <template #default="{ row }">
            <DictTag
              :type="'project_problem_proposing_department'"
              :value="row.proposingDepartment"
            />
          </template>
        </vxe-column>
        <vxe-column title="不良比例" field="rejectRatio" width="100" align="center">
          <template #default="{ row }">
            {{ row.rejectRatio + (row.rejectRatio ? '%' : '') }}
          </template>
        </vxe-column>
        <vxe-column title="解决措施" field="measures" width="200" align="left" />
        <vxe-column
          title="责任人"
          field="director"
          width="120"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.director) }}
          </template>
        </vxe-column>
        <vxe-column
          title="执行人"
          field="coordinate"
          width="120"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.coordinate) }}
          </template>
        </vxe-column>
        <vxe-column title="提出人" field="proposal" width="120" align="center">
          <template #default="{ row }">
            {{ getUserNickName(row.proposal) }}
          </template>
        </vxe-column>
        <vxe-column
          title="提出时间"
          field="timeOfProposal"
          :formatter="dateFormatter4"
          width="90"
          align="center"
        />
        <vxe-column
          title="计划完成"
          field="timeOfPlan"
          :formatter="dateFormatter4"
          width="90"
          align="center"
          fixed="right"
        />
        <vxe-column
          title="验证时间"
          field="timeOfVerification"
          :formatter="dateFormatter4"
          width="90"
          align="center"
        />
        <vxe-column title="效果确认" field="effect" width="120" align="center" />
        <vxe-column title="确认人" field="effectPerson" width="120" align="center">
          <template #default="{ row }">
            {{ getUserNickName(row.effectPerson) }}
          </template>
        </vxe-column>
      </vxe-table>
      <Pagination
        :total="total"
        v-model:page="formData.pageNo"
        v-model:limit="formData.pageSize"
        @pagination="onList"
      />
    </div>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { getIntDictOptions, getStrDictOptions, getDictLabel } from '@/utils/dict'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { dateFormatter4 } from '@/utils/formatTime';
import { ProblemApi } from '@/api/project/problem';
import download from '@/utils/download';

const formData = ref({
  basicsName: undefined,
  basicsStatus: undefined,
  content: undefined,
  type: undefined,
  status: undefined,
  level: undefined,
  category: undefined,
  module: undefined,
  proposingDepartment: undefined,
  direcotr: undefined,
  coordinate: undefined,
  timeOfProposal: undefined,
  timeOfPlan: undefined,
  pageNo: 1,
  pageSize: 20
})

const userList = ref<UserVO[]>([])
const problemList = ref<any[]>([])
const toolbarRef = ref()
const tableRef = ref()
const loading = ref(false)
const total = ref(0)
const message = useMessage()

const handleList = async () => {
  formData.value.pageNo = 1
  onList()
}
const onList = async () => {
  loading.value = true
  try{
    const res = await ProblemApi.getProblemExportPage(formData.value)
    problemList.value = res.list
    total.value = res.total
  }finally{
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    basicsName: undefined,
    basicsStatus: undefined,
    content: undefined,
    type: undefined,
    status: undefined,
    level: undefined,
    category: undefined,
    module: undefined,
    proposingDepartment: undefined,
    direcotr: undefined,
    coordinate: undefined,
    timeOfProposal: undefined,
    timeOfPlan: undefined,
    pageNo: 1,
    pageSize: 20
  }
  handleList()
}

const handleExport = async() => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    loading.value = true
    const data = await ProblemApi.exportProblemMulti(formData.value)
    download.excel(data, '问题信息.xlsx')
  } catch {
  } finally {
    loading.value = false
  }
}

/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}

const onListUser = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}
onMounted(() => {
  onListUser()
  onList()
})
</script>
