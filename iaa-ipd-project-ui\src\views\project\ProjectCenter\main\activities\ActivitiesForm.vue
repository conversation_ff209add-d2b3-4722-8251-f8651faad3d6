<template>
  <NoModalDrawer v-model="visiable" :title="formTitle" size="50%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ formTitle }}</div>
        <div class="flex" v-if="!formData?.isCrux">
          <el-tooltip content="修改活动">
            <el-button
              link
              @click="updateActivities"
              v-getPermi="['activities_modify']"
              v-if="formData.status !== 10 && formData.progress !== 100"
              :loading="loading"
            >
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除活动">
            <el-button
              link
              @click="deleteActivities"
              v-getPermi="['activities_delete']"
              v-if="formData.status !== 10 && formData.progress !== 100"
              :loading="loading"
            >
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <template #default>
      <el-tooltip
        content="任务完成确认"
        v-if="!edit && formData.progress == 90 && formData.status !== 10 && allowPermission"
      >
        <el-button
          type="success"
          class="position-fixed w-20px !h-80px !p-0"
          style="left: 50%; top: calc(50% + 50px)"
          @click="completeActivities"
        >
          <Icon icon="ep:check" :size="22" />
        </el-button>
      </el-tooltip>
      <el-alert
        v-if="formData.status === 10"
        type="warning"
        show-icon
        description="当前活动有相关流程审批中"
        close-text="查看流程"
        @close="toBpm(formData.currentProcessInstanceId!)"
      />
      <el-alert
        v-if="!allowTheOutput"
        type="error"
        show-icon
        description="当前活动前置活动未完成，请等待前置活动完成，详情请前往依赖活动菜单查看"
        :closable="false"
      />
      <el-alert
        v-if="!allowToComplete && formData.progress == 90"
        type="warning"
        show-icon
        description="前置活动未完成，当前活动不允许完成，请等待前置活动完成，详情请前往依赖活动菜单查看"
        :closable="false"
      />
      <el-tabs
        v-model="currentTab"
        class="position-sticky top-0 z-10 bg-#fff"
        @tab-change="onTableChange"
      >
        <el-tab-pane label="基础信息" name="info" />
        <el-tab-pane label="输出物" name="target" v-if="!edit" />
        <el-tab-pane label="依赖活动" name="depend" v-if="!edit" />
        <el-tab-pane label="日志" name="log" v-if="!edit" />
      </el-tabs>
      <!-- 基础信息-->
      <template v-if="currentTab === 'info'">
        <ActivitiesInfo
          ref="activitiesInfoRef"
          :user-list="props.userList"
          :data="formData"
          :edit="edit"
        />
      </template>
      <template v-else-if="currentTab === 'target'">
        <ActivitiesTarget
          ref="activitiesTargetRef"
          :data="formData"
          :edit="edit"
          :file-template-list="fileTemplateList"
          v-model:allowTheOutput="allowTheOutput"
        />
      </template>
      <template v-else-if="currentTab === 'depend' && formData.mold == 0">
        <vxe-table
          :data="formData.dependencies"
          :header-cell-style="{ padding: '5px' }"
          :cell-style="{ padding: '5px' }"
          v-if="formData.dependencies && formData.dependencies.length > 0"
        >
          <vxe-column title="活动主题" field="name" />
          <vxe-column title="活动进度">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress"
                class="w-100%"
                :text-inside="true"
                :stroke-width="20"
              />
            </template>
          </vxe-column>
          <vxe-column title="时间">
            <template #default="{ row }">
              {{ formatToDate(row.startDate, 'YYYY.MM.DD') }}-{{
                formatToDate(row.endDate, 'YYYY.MM.DD')
              }}
            </template>
          </vxe-column>
          <vxe-column title="负责人" width="10%">
            <template #default="{ row }">
              <user-avatar-list
                v-model="row.director"
                :user-list="props.userList"
                :size="30"
                :limit="3"
                :add="false"
                :visiable-user-list="getVisiableUserList()"
              />
            </template>
          </vxe-column>
          <vxe-column title="执行人" width="10%">
            <template #default="{ row }">
              <user-avatar-list
                v-model="row.coordinate"
                :user-list="props.userList"
                :size="30"
                :limit="3"
                :add="false"
                :visiable-user-list="getVisiableUserList()"
              />
            </template>
          </vxe-column>
          <vxe-column title="任务关系">
            <template #default="{ row }">
              <DictTag type="project_activities_relation" :value="row.dependencyType" />
            </template>
          </vxe-column>
        </vxe-table>
        <el-empty description="无依赖活动" v-else />
      </template>
      <template v-else-if="currentTab === 'log'">
        <LogForm
          ref="logFormRef"
          category="activities"
          :base-id="formData.id"
          :user-list="props.userList"
        />
      </template>
    </template>
    <template #footer v-if="['info', 'target'].includes(currentTab) && !edit">
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="props.userList"
        :activities-list="props.activitiesList"
        @send="onSaveComment"
        v-if="currentTab == 'info'"
      />
      <FileUpload
        v-if="
          currentTab == 'target' &&
          (([0, 4].includes(formData.targetType!) &&
            allowTheOutput &&
            formData.status !== 10 &&
            formData.progress !== 100 &&
            allowPermission) ||
            basicsInfo?.categoryIds?.includes(8))
        "
        ref="fileUploadRef"
        category="activities"
        :dynamic-id="formData.id"
        :file-template-list="
          fileTemplateList.filter((item) => formData.targetTemplateIds?.includes(item.id))
        "
        :target-type="formData.targetType"
        :target-docking-id="formData.targetDockingId"
        @success="activitiesTargetRef?.onListAttachment"
      />
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { UserVO } from '@/api/system/user'
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { CommentApi, CommentSaveReqVO } from '@/api/infra/comment'
import { CommentVO } from '@/components/CommentInput/comment'
import { formatToDate } from '@/utils/dateUtil'
import ActivitiesTarget from './components/ActivitiesTarget.vue'
import ActivitiesInfo from './components/ActivitiesInfo.vue'
import LogForm from '../../details/components/LogForm.vue'
import FileUpload from '../components/FileUpload.vue'
import { getVisiableUserList } from '../util/permission'
import { ActivitiesFlowApi } from '@/api/bpm/activities'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import { BasicsVO } from '@/api/project/basics'
import { useCache } from '@/hooks/web/useCache'

const router = useRouter()
const visiable = ref(false)
const formTitle = ref('')
const currentTab = ref('info')
const formData = ref<ActivitiesVO>({})
const message = useMessage()
const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const { wsCache } = useCache()
const activitiesInfoRef = ref()
const activitiesTargetRef = ref()
const basicsInfo = ref<any>()

const logFormRef = ref()

const edit = ref(false)
const loading = ref(false)
const { getUser } = useUserStore()

const props = defineProps({
  userList: propTypes.oneOf<UserVO[]>([]).isRequired,
  activitiesList: propTypes.oneOf<ActivitiesVO[]>([]).isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([])
})

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'info':
      activitiesInfoRef.value?.refreshComment()
      break
    case 'target':
      activitiesTargetRef.value?.onListAttachment()
      break
    case 'log':
      logFormRef.value?.refreshLog()
      break
  }
}

/** 发送评论 */
const onSaveComment = async () => {
  if (typeof commentData.value.imgs == 'string') {
    if (!commentData.value.imgs) {
      commentData.value.imgs = []
    } else {
      commentData.value.imgs = [commentData.value.imgs]
    }
  }
  const comment: CommentSaveReqVO = {
    moduleId: 'activities' + formData.value.id,
    content: commentData.value.content,
    imgUrls: commentData.value.imgs,
    parentId: -1,
    replyCommentId: -1
  }
  await CommentApi.createComment(comment)
  message.success('发送成功')
  commentData.value = {
    content: '',
    imgs: ''
  }
  activitiesInfoRef.value?.refreshComment()
}

/** 打开表单 */
const openForm = async (row: ActivitiesVO) => {
  formTitle.value = row.name!
  visiable.value = true
  formData.value = row
  await nextTick()
  onTableChange()
  basicsInfo.value = wsCache.get('PROJECT_BASICS_INFO') as BasicsVO
}

const allowTheOutput = computed(() => {
  if (!formData.value.dependencies || formData.value.dependencies.length == 0) {
    return true
  }
  let allow = true
  formData.value.dependencies.forEach((item) => {
    if (item.dependencyType == 'fs' && item.progress != 100) {
      allow = false
    }
  })

  return allow
})

const allowToComplete = computed(() => {
  if (!formData.value.dependencies || formData.value.dependencies.length == 0) {
    return true
  }
  let allow = true
  formData.value.dependencies.forEach((item) => {
    if (item.dependencyType == 'ff' && item.progress != 100) {
      allow = false
    }
  })
  return allow
})

const allowPermission = computed(() => {
  return (
    formData.value.director?.includes(getUser.id) || formData.value.coordinate?.includes(getUser.id)
  )
})

const emits = defineEmits(['success', 'refresh'])

const updateActivities = async () => {
  loading.value = true
  try {
    if (!edit.value) {
      edit.value = true
      currentTab.value = 'info'
      return
    }

    const data = await activitiesInfoRef.value?.getValue()
    if (data.modifyInfo?.length == 0) {
      message.warning('没有修改活动信息')
      edit.value = false
      return
    }
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入变更原因', '活动', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '修改原因不能为空'
    })
    data.modifyInfo.push({
      modifyField: 'update',
      modifyFieldName: '活动修改原因：' + value,
      beforeValue: ''
    })
    await ActivitiesFlowApi.updateActivities({ ...data, activitiesId: data.id })
    message.success('活动修改流程创建成功，请等待审批完成')
    const res = await ActivitiesApi.getActivities(formData.value.id!)
    formData.value = res
    edit.value = false
    emits('refresh')
  } finally {
    loading.value = false
  }
}

const deleteActivities = async () => {
  loading.value = true
  try {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '删除活动', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })
    const data = await activitiesInfoRef.value?.getValue()
    if (data.modifyInfo?.length == 0) {
      data.modifyInfo = []
      data.modifyInfo.push({
        modifyField: 'delete',
        modifyFieldName: '活动删除审批处理,原因：' + value,
        beforeValue: '',
        afterValue: ''
      })
    }
    await ActivitiesFlowApi.deleteActivities({ ...data, activitiesId: data.id })
    message.success('活动修改流程创建成功，请等待审批完成')
    const res = await ActivitiesApi.getActivities(formData.value.id!)
    formData.value = res
    edit.value = false
    emits('refresh')
  } finally {
    loading.value = false
  }
}

const attachmentList = ref<AttachmentRespVO[]>([])
/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

const completeActivities = async () => {
  loading.value = true
  try {
    await message.confirm('确定当前活动已经完成?')
    const data = await activitiesInfoRef.value?.getValue()
    await onListAttachment()
    const exists = attachmentList.value.some((item) => item.approvalStatus === 0)
    if (exists) {
      message.error('存在未完成审签的输出物，审批通过后将会自动发起完成流程')
      return
    }
    await ActivitiesFlowApi.completeActivities({ ...data, activitiesId: data.id })
    message.success('活动完成审批发起成功，请等待审批完成')
    const res = await ActivitiesApi.getActivities(formData.value.id!)
    formData.value = res
    edit.value = false
    emits('refresh')
  } finally {
    loading.value = false
  }
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
.el-tabs {
  display: block;
  height: 43px !important;
  --el-tabs-header-height: 43px !important;
  margin-bottom: 10px;
}
:deep(.el-tabs__content) {
  padding: 0;
}
</style>
