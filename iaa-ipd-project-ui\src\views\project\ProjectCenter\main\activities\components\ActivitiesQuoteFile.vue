<template>
  <Dialog title="引用已归档文件" v-model="quoteVisible">
    <vxe-table
      height="600"
      :header-cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        backgroundColor: '#fafafa',
        color: 'var(--primary-text-color)'
      }"
      :row-style="{
        cursor: 'pointer'
      }"
      :cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        color: 'var(--primary-text-color)'
      }"
      round
      border
      auto-resize
      :row-config="{ isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
      :column-config="{ resizable: true, isHover: true }"
      :data="attachmentList"
      show-overflow
      :loading="loading"
      ref="attachmentTableRef"
    >
      <vxe-column type="checkbox" width="50px" fixed="left" />
      <vxe-column
        title="输出物名称"
        field="name"
        :filters="nameOptions"
        :filter-render="FilterValue.textFilterRender"
      >
        <template #default="{ row }">
          <el-button type="primary" link @click="emits('view', row.name, row.processInstanceId)">
            {{ row.name }}
          </el-button>
        </template>
      </vxe-column>
      <vxe-column
        title="所属活动"
        field="activitiesName"
        :filters="activitiesNameOptions"
        :filter-render="FilterValue.textFilterRender"
      />
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="onChecked">确定</el-button>
    </template>
  </Dialog>
  <Dialog title="已选列表" v-model="checkedVisible">
    <div class="max-h-400px overflow-y-auto">
      <el-form
        v-for="(item, index) in checkedList"
        :key="index"
        label-width="90px"
        class="custom-border-form custom-form mt-5px"
        :rules="formDataRules"
        :model="item"
        ref="formRef"
      >
        <el-form-item label="文件名称" prop="name">
          <el-input v-model="item.name" />
        </el-form-item>
        <el-form-item label="参考模板" prop="templateId">
          <el-select v-model="item.templateId">
            <el-option
              v-for="file in showFileTemplate"
              :key="file.id"
              :label="file.name"
              :value="file.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="onSubmit">提交</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { AttachmentApi } from '@/api/project/attachment'
import { AttachmentFlowApi } from '@/api/bpm/attachment'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'

const attachmentList = ref<any[]>([])
const loading = ref(false)
const nameOptions = ref([{ data: '' }])
const activitiesNameOptions = ref([{ data: '' }])

const quoteVisible = ref(false)
const emits = defineEmits(['view','success'])
const props = defineProps({
  basicsId: propTypes.number.isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  targetTemplateIds: propTypes.oneOf<number[]>([]).def([]),
  activitiesId: propTypes.number.def(0)
})

const openForm = async () => {
  loading.value = true
  try {
    quoteVisible.value = true
    attachmentList.value = await AttachmentApi.getAttachmentByBasicsId(props.basicsId)
  } finally {
    loading.value = false
  }
}
const message = useMessage()
const attachmentTableRef = ref()
const checkedVisible = ref(false)
const checkedList = ref<any[]>([])
const formDataRules = {
  name: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  url: [{ required: true, message: '路径不能为空', trigger: 'blur' }],
  templateId: [
    {
      required: props.targetTemplateIds.length > 0,
      message: '请选择绑定的参考模板',
      trigger: 'blur'
    }
  ]
}
const showFileTemplate = computed(() => {
  return props.fileTemplateList.filter((item) => props.targetTemplateIds.includes(item.id))
})

const onChecked = () => {
  const checkedData = attachmentTableRef.value.getCheckboxRecords(false)
  if (!checkedData || checkedData.length == 0) {
    message.warning('没有选择要引用的文件')
    return
  }
  checkedList.value = checkedData.map((item) => {
    item.templateId = undefined
    return item
  })
  checkedVisible.value = true
}

const onSubmit = async () => {
  loading.value = true
  try {
    const data = checkedList.value.map((item: any) => {
      return {
        category: 'quote',
        dynamicId: props.activitiesId,
        name: item.name,
        infraFileId: item.infraFileId,
        templateId: item.templateId
      }
    })
    await AttachmentFlowApi.createAttachmentFlowBatch(data)
    message.success('流程发起成功')
    checkedList.value = []
    checkedVisible.value = false
    quoteVisible.value = false
    emits('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
