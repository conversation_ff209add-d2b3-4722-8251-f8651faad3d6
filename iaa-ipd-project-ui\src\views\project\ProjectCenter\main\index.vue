<template>
  <div class="main-container">
    <div class="header-container">
      <el-watermark
        :content="initWatermark.content"
        :font="{ fontSize: 16, color: initWatermark.color }"
      >
        <Process
          :process="props.basicsInfo?.progress"
          :key-node-date="activitiesList"
          :create-date="props.basicsInfo?.createTime"
          class="process-container"
        />
        <el-tabs
          type="border-card"
          class="header-tabs-container"
          v-model="currentTab"
          @tab-change="onTableChange"
        >
          <el-tab-pane label="活动" name="activities" :lazy="true">
            <Activities
              :basicsInfo="props.basicsInfo"
              :custom="custom"
              :templateCategory="templateCategory"
              v-if="currentTab === 'activities'"
            />
          </el-tab-pane>
          <el-tab-pane label="问题" name="problem" :lazy="true">
            <Problem
              :basicsInfo="props.basicsInfo"
              :templateCategory="templateCategory"
              v-if="currentTab === 'problem'"
            />
          </el-tab-pane>
          <el-tab-pane label="风险" name="risk">
            <Risk
              :basics-info="props.basicsInfo"
              :templateCategory="templateCategory"
              v-if="currentTab === 'risk'"
            />
          </el-tab-pane>
          <el-tab-pane label="技术评审" name="technical">
            <Technical
              :basics-info="props.basicsInfo"
              :template-category="props.templateCategory"
              :activities="activitiesList"
              v-if="currentTab === 'technical'"
            />
          </el-tab-pane>
          <el-tab-pane label="决策评审" name="tcp">
            <Decision :basics-info="props.basicsInfo" v-if="currentTab === 'tcp'" />
          </el-tab-pane>
          <!-- <el-tab-pane label="内审" name="internal" /> -->
          <el-tab-pane label="会议纪要" name="conference">
            <Conference
              :basics-info="props.basicsInfo"
              :template-category="props.templateCategory"
              v-if="currentTab === 'conference'"
            />
          </el-tab-pane>
          <!-- <el-tab-pane label="工作总结" name="summary">
            <Summary :basics-info="props.basicsInfo" v-if="currentTab === 'summary'" />
          </el-tab-pane> -->
          <el-tab-pane label="输出物" name="document">
            <TargetDocument
              :basics-info="props.basicsInfo"
              :template-category="props.templateCategory"
              v-if="currentTab === 'document'"
            />
          </el-tab-pane>
          <el-tab-pane label="甘特图" name="gantt">
            <GanttChart :basics-info="props.basicsInfo" v-if="currentTab === 'gantt'" />
          </el-tab-pane>
          <el-tab-pane
            label="费用"
            name="expense"
            v-if="
              getRolePermission('pm', getUser.id) ||
              getRolePermission('financial', getUser.id) ||
              getUser.id == 1 ||
              permissions.includes('project:expense:view')
            "
          >
            <Expense :basics-info="props.basicsInfo" v-if="currentTab === 'expense'" />
          </el-tab-pane>
          <el-tab-pane
            label="看板"
            name="kanban"
            v-if="getRolePermission('pm', getUser.id) || getUser.id == 1"
          >
            <Kanban :basics-info="props.basicsInfo" v-if="currentTab === 'kanban'" />
          </el-tab-pane>
          <el-tab-pane
            label="变更"
            name="change"
            v-if="
              getRolePermission('pm', getUser.id) || getUser.id == 1 || checkPermi(['project:change:view'])
            "
          >
            <Change :basics-info="props.basicsInfo" />
          </el-tab-pane>
        </el-tabs>
      </el-watermark>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { BasicsVO } from '@/api/project/basics'
import Process from './components/Process.vue'
import Activities from './activities/index.vue'
import Problem from './problem/index.vue'
import Risk from './risk/index.vue'
import Technical from './technical/index.vue'
import Decision from './decision/index.vue'
import Conference from './conference/index.vue'
import TargetDocument from './document/index.vue'
import GanttChart from './gantt-chart/index.vue'
import Expense from './expense/index.vue'
// import Summary from './summary/index.vue'
import Kanban from './kanban/index.vue'
import Change from './change/index.vue'
import { ActivitiesApi } from '@/api/project/activities'
import { debounce } from 'min-dash'
// import { getStrDictOptions } from '@/utils/dict'
import { getRolePermission } from './util/permission'
import { useUserStore } from '@/store/modules/user'
import { checkPermi } from '@/utils/permission'

const currentTab = ref('activities')
const { wsCache } = useCache()
const activitiesList = ref<any[]>([])

const { getUser } = useUserStore()

const permissions = wsCache.get(CACHE_KEY.USER).permissions

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  },
  custom: {
    type: Boolean,
    default: false
  },
  templateCategory: {
    type: String
  }
})

const initWatermark = computed(() => {
  switch (props.basicsInfo?.status) {
    case 1:
      return { content: '项目已暂停', color: 'rgba(205, 161, 0, 0.15)' }
    case 2:
      return { content: '项目已完结', color: 'rgba(0, 205, 38, 0.15)' }
    case 4:
      return { content: '项目创建中', color: 'rgba(205,0, 38, 0.15)' }
    default:
      return { content: '', color: 'rgba(0, 205, 38, 0.15)' }
  }
})

const onTableChange = () => {
  wsCache.set(CACHE_KEY.PROJECT_CURRENT_TAB, currentTab.value)
}

const onListCruxActivities = async () => {
  const res = await ActivitiesApi.getActivitiesList({
    basicsId: props.basicsInfo.id,
    hasCrux: true
  })
  activitiesList.value = res
}

const debouncedFetch = debounce(() => {
  onListCruxActivities()
}, 300)
watch(
  () => props.basicsInfo?.id,
  () => {
    if (props.basicsInfo?.id) {
      debouncedFetch()
    }
  },
  { immediate: true }
)

onMounted(() => {
  const form = wsCache.get('project_page_show_form')
  if (form) {
    currentTab.value = form.page
  } else {
    currentTab.value = wsCache.get(CACHE_KEY.PROJECT_CURRENT_TAB) || 'activities'
  }
})
</script>

<style lang="scss" scoped>
/** 容器样式 */
.main-container {
  height: 100%;
  background-color: #fff;
  .header-container {
    background-color: #fff;
  }
}

/** header tabs 样式 */
:deep(.header-tabs-container) {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  & > .el-tabs__header {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;

    .el-tabs__nav {
      padding: 0 20px;
    }
    .el-tabs__item {
      height: 2rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  & > .el-tabs__content {
    padding: 0;
  }
}
</style>
