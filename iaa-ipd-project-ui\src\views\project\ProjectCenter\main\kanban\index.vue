<template>
  <ActivitiesContainer :basics-info="props.basicsInfo!" :node-list="[]">
    <template #activities-table>
      <div class="h-95% w-full overflow-auto">
        <ContentWrap title="活跃统计">
          <template #header>
            <el-radio-group v-model="dailyViewsType" size="small">
              <el-radio-button label="年" value="year" />
              <el-radio-button label="月" value="month" />
              <el-radio-button label="周" value="week" />
            </el-radio-group>
            <el-date-picker
              :type="dailyViewsType"
              v-model="dailyViewsDate"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              size="small"
              :clearable="false"
              @change="getDailyViewsList"
            />
          </template>
          <div id="daily-charts" class="w-full h-300px"></div>
        </ContentWrap>

        <!-- <ContentWrap title="项目工时(天)">
          <template #header>
            <el-tooltip
              content="注意：数据范围为当前时间及之前应完成的事项，未完成活动工时按当前时间减计划开始时间计算"
            >
              <Icon icon="ep:warning-filled" />
            </el-tooltip>
          </template>
          <div id="activities-hours-line" class="w-full h-300px"></div>
          <div id="problem-hours-line" class="w-full h-300px"></div>
        </ContentWrap> -->

        <ContentWrap title="状态分布">
          <div class="flex">
            <div id="activities-status" class="w-25% h-150px"></div>
            <div id="problem-status" class="w-25% h-150px"></div>
            <div id="risk-status" class="w-25% h-150px"></div>
            <div id="technical-status" class="w-25% h-150px"></div>
          </div>
        </ContentWrap>

        <ContentWrap title="问题分布">
          <div class="flex">
            <div id="problem-distribution-category" class="w-25% h-150px"></div>
            <div id="problem-distribution-module" class="w-25% h-150px"></div>
            <div id="problem-distribution-level" class="w-25% h-150px"></div>
            <div id="problem-distribution-dept" class="w-25% h-150px"></div>
          </div>
        </ContentWrap>

        <!-- <ContentWrap title="偏差统计">
          <template #header>
            <el-tooltip
              content="注意：数据范围为当前时间及之前应完成的事项，偏差统计为实际工时减计划工时的平均"
            >
              <Icon icon="ep:warning-filled" />
            </el-tooltip>
          </template>
          <div class="flex">
            <div id="deviation-activities" class="h-300px w-50%"></div>
            <div id="deviation-problem" class="h-300px w-50%"></div>
          </div>
        </ContentWrap> -->
        <ContentWrap title="项目关系">
          <div class="h-400px">
            <RelationGraph ref="relationGraphRef" :options="options" />
          </div>
        </ContentWrap>
      </div>
    </template>
  </ActivitiesContainer>
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { KanbanApi } from '@/api/project/kanban'
import { BasicsApi } from '@/api/project/basics'
import * as LineChart from '@/views/Home/kanban/line'
import * as PieChart from '@/views/Home/kanban/pie'
import * as echarts from 'echarts'
import { getDictLabel } from '@/utils/dict'
import { DailyViewsApi } from '@/api/project/dailyviews'
import moment from 'moment'
import RelationGraph from 'relation-graph-vue3'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})
const workHoursList = ref<any[]>([])
const labelOption: any = {
  show: true,
  position: 'insideBottom' as any['position'],
  distance: 15 as any['distance'],
  align: 'left' as any['align'],
  verticalAlign: 'middle' as any['verticalAlign'],
  rotate: 90 as any['rotate'],
  formatter: '{c}',
  fontSize: 12,
  rich: {
    name: {}
  }
}
const initWorkHours = async () => {
  const res = await KanbanApi.getWorkingHours({ basicsId: props.basicsInfo!.id! })
  workHoursList.value = res
  await nextTick()
  showLineChart('活动工时', ['计划工时', '实际工时'], 'activities', 'activities-hours-line')
  showLineChart('问题工时', ['计划工时', '实际工时'], 'problem', 'problem-hours-line')
}

const initProblemDistribution = async () => {
  const res = await KanbanApi.getProblemDistribution({ basicsId: props.basicsInfo!.id! })
  await nextTick()
  showPieChart(
    '分类统计',
    'problem-distribution-category',
    'category',
    res.category,
    'project_problem_category'
  )
  showPieChart(
    '责任模块',
    'problem-distribution-module',
    'module',
    res.module,
    'project_problem_module'
  )
  showPieChart(
    '等级分布',
    'problem-distribution-level',
    'level',
    res.level,
    'project_problem_level'
  )
  showPieChart(
    '参与人员',
    'problem-distribution-dept',
    'department',
    res.department,
    'project_problem_proposing_department'
  )
  showPieChart(
    '活动',
    'activities-status',
    'status',
    res.activitiesStatus,
    'project_activities_status'
  )
  showPieChart('问题', 'problem-status', 'status', res.problemStatus, 'project_activities_status')
  showPieChart('风险', 'risk-status', 'status', res.riskStatus, 'project_risk_status')
  showPieChart(
    '技术评审',
    'technical-status',
    'status',
    res.technicalStatus,
    'project_activities_status'
  )
}

const showPieChart = async (
  title: string,
  id: string,
  type: string,
  data: any[],
  dictType: string
) => {
  PieChart.refreshChart(
    data.map((item) => {
      return {
        name: getDictLabel(dictType, item[type]),
        value: item.count
      }
    }),
    echarts.init(document.getElementById(id)!),
    title
  )
}

const initDeviation = async () => {
  const res = await KanbanApi.getDeviation({ basicsId: props.basicsInfo!.id! })
  await nextTick()
  LineChart.refreshChartVertical(
    '活动偏差',
    res.filter((item) => item.type == 'activities').map((item) => item.nickname),
    res.filter((item) => item.type == 'activities').map((item) => item.value),
    echarts.init(document.getElementById('deviation-activities')!)
  )
  LineChart.refreshChartVertical(
    '问题偏差',
    res.filter((item) => item.type == 'problem').map((item) => item.nickname),
    res.filter((item) => item.type == 'problem').map((item) => item.value),
    echarts.init(document.getElementById('deviation-problem')!)
  )
}

const showLineChart = async (title: string, legend: string[], type: string, id: string) => {
  const chart = echarts.init(document.getElementById(id)!)
  chart.clear()
  LineChart.refreshChartMulti(
    title,
    legend,
    workHoursList.value.filter((item) => item.type == type).map((item) => item.nickname),
    [
      {
        name: '计划工时',
        type: 'bar',
        barGap: 0,
        label: labelOption,
        emphasis: {
          focus: 'series'
        },
        data: workHoursList.value.filter((item) => item.type == type).map((item) => item.plan),
        itemStyle: {
          normal: {
            color: '#63b4ff'
          }
        }
      },
      {
        name: '实际工时',
        type: 'bar',
        barGap: 0,
        label: labelOption,
        emphasis: {
          focus: 'series'
        },
        data: workHoursList.value.filter((item) => item.type == type).map((item) => item.actual),
        itemStyle: {
          normal: {
            color: '#fac858'
          }
        }
      }
    ],
    chart
  )
}

const dailyViewsType = ref<any>('month')
const dailyViewsDate = ref(moment().format('YYYY-MM-DD'))
const getDailyViewsList = async () => {
  const res = await DailyViewsApi.getDailyViewsList({
    basicsId: props.basicsInfo?.id,
    dateType: dailyViewsType.value,
    date: dailyViewsDate.value
  })
  const userCountMap = res.reduce(
    (acc, item) => {
      const key = item.userName
      acc[key] = (acc[key] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )
  const chartData = Object.entries(userCountMap)
    .map(([name, value]: [string, number]) => ({ name, value }))
    .sort((a, b) => b.value - a.value) // 按 value 降序排序
  LineChart.refreshOne(
    '活跃统计',
    chartData.map((item) => item.name),
    chartData.map((item) => item.value),
    echarts.init(document.getElementById('daily-charts')!)
  )
}

const options = {
  defaultNodeShape: 1, //默认的节点形状，0:圆形；1:矩形
  defaultExpandHolderPosition: 'bottom', //节点展开关闭的按钮位置
  defaultLineShape: 4, //默认的线条样式（1:直线/2:样式2/3:样式3/4:折线/5:样式5/6:样式6）
  defaultJunctionPoint: 'tb', //默认的连线与节点接触的方式（border:边缘/ltrb:上下左右/tb:上下/lr:左右）当布局为树状布局时应使用tb或者lr，这样才会好看
  defaultNodeBorderWidth: 0.2, //节点边框粗细
  defaultLineColor: 'rgba(0, 186, 189, 1)', //默认的线条颜色
  defaultNodeColor: 'rgba(0, 206, 209, 1)', //默认的节点背景颜色
  defaultNodeWidth: '130', //节点宽度
  defaultNodeHeight: '80', //节点高度
  defaultFocusRootNode: true, //默认为根节点添加一个被选中的样式
  moveToCenterWhenResize: false, //当图谱的大小发生变化时，是否重新让图谱的内容看起来居中
  layouts: [
    {
      label: '中心',
      layoutName: 'tree', //布局方式（tree树状布局/center中心布局/force自动布局）
      layoutClassName: 'seeks-layout-center', //当使用这个布局时，会将此样式添加到图谱上
      defaultJunctionPoint: 'border', //默认的连线与节点接触的方式
      defaultNodeShape: 0, //默认的节点形状，0:圆形；1:矩形
      defaultLineShape: 1, //默认的线条样式（1:直线/2:样式2/3:样式3/4:折线/5:样式5/6:样式6）
      centerOffset_y: 130, //根节点y坐标偏移量（针对项目配置单位为px）
      min_per_width: 150, //节点距离限制:节点之间横向距离最小值
      min_per_height: 180 //节点距离限制:节点之间纵向距离最小值
    }
  ]
}

const relationGraphRef = ref()

const getRelationList = async () => {
  if (!props.basicsInfo?.type) return
  let res = [] as any[]
  if (props.basicsInfo.type === '11') {
    res = await BasicsApi.getChildRelation(props.basicsInfo.id!)
  } else {
    res = await BasicsApi.getParentOrChildRelation(props.basicsInfo.id!)
  }
  console.log(res)
  await nextTick()
  const jsonData = {
    rootId: String(props.basicsInfo.id),
    nodes: [] as any[],
    lines: [] as any[]
  }
  res.forEach((item) => {
    // 添加节点
    jsonData.nodes.push({ id: String(item.fromId), text: String(item.fromName) })
    jsonData.nodes.push({ id: String(item.toId), text: String(item.toName) })
    jsonData.lines.push({ from: String(item.fromId), to: String(item.toId) })
  })
  const nodeMap = new Map()
  jsonData.nodes = jsonData.nodes.filter((node) => {
    if (nodeMap.has(node.id)) return false
    nodeMap.set(node.id, true)
    return true
  })
  relationGraphRef.value.setJsonData(jsonData)
}

watch(
  () => props.basicsInfo?.id,
  async () => {
    if (props.basicsInfo?.id) {
      // await initWorkHours()
      await initProblemDistribution()
      // await initDeviation()
      await getDailyViewsList()
      getRelationList()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-card__header),
:deep(.el-card__body) {
  padding: 10px;
}
</style>
